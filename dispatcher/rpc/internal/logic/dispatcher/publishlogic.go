package dispatcherlogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

type PublishLogic struct {
	*BaseLogic
}

func NewPublishLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PublishLogic {
	return &PublishLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *PublishLogic) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	loginUser := userinfo.FromContext(l.ctx)
	if loginUser != nil {
		in.UserId = loginUser.Account
	}

	var publisher Publisher
	switch in.GetPublishType() {
	case pb.PublishType_PublishType_API_COMPONENT_GROUP:
		publisher = NewComponentGroupPublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_API_CASE:
		publisher = NewCasePublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_API_SUITE:
		publisher = NewSuitePublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_INTERFACE_CASE:
		publisher = NewInterfaceCasePublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_INTERFACE_DOCUMENT:
		publisher = NewInterfacePublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_API_PRECISION_SUITE:
		publisher = NewPrecisionSuitePublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_PRECISION_INTERFACE_DOCUMENT:
		publisher = NewPrecisionInterfacePublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_API_PLAN:
		publisher = NewPlanPublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_UI_CASE:
		publisher = NewUICasePublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_UI_SUITE:
		publisher = NewUISuitePublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_UI_PLAN:
		publisher = NewUIPlanPublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_API_SERVICE:
		publisher = NewServicePublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_PERF_CASE:
		publisher = NewPerfCasePublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_PERF_SUITE:
		publisher = NewPerfSuitePublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_PERF_PLAN:
		if err = l.checkPerfPlanExecution(in); err != nil {
			return nil, err
		}
		publisher = NewPerfPlanPublisher(l.ctx, l.svcCtx)
	case pb.PublishType_PublishType_STABILITY_PLAN:
		publisher = NewStabilityPlanPublisher(l.ctx, l.svcCtx)
	default:
		return nil, errorx.Errorf(errorx.ValidateParamError, "执行类型无效: %s", in.GetPublishType())
	}

	defer func() {
		if err != nil {
			l.Errorf("Publish.Panic, request=%s, err: %s", jsonx.MarshalToStringIgnoreError(in), err)
			publisher.Panic(in, err)
		}

		l.Infof(
			"PublishLogic.Publish in = %s, out = %s",
			jsonx.MarshalToStringIgnoreError(in), jsonx.MarshalToStringIgnoreError(resp),
		)
	}()

	err = publisher.Setup(in)
	if err != nil {
		return nil, err
	}

	err = publisher.CreateRecord(in)
	if err != nil {
		return nil, err
	}

	return publisher.Publish(in)
}
