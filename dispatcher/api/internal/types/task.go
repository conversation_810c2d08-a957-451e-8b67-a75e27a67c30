package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type ComponentKey struct {
	Id      string `json:"id"`                         // 执行对象的id
	Type    int64  `json:"type"`                       // 执行对象的类型
	Version string `json:"version,omitempty,optional"` // 执行对象的版本号
}

type UIAgentComponent struct {
	Name          string                             `json:"name"`           // 组件名称
	ApplicationId string                             `json:"application_id"` // 应用配置ID
	StepByStep    bool                               `json:"step_by_step"`   // 是否分步执行
	Steps         []*types.UIAgentComponentStep      `json:"steps"`          // 步骤列表
	Expectation   *types.UIAgentComponentExpectation `json:"expectation"`    // 期望结果
	Device        *UIAgentDevice                     `json:"device"`         // 设备信息
	Reinstall     bool                               `json:"reinstall"`      // 是否重新安装
	Restart       bool                               `json:"restart"`        // 是否重启应用
}

type UIAgentDevice struct {
	ProjectDevice *UIAgentProjectDevice `json:"project_device,omitempty,optional"` // 项目设备
	UserDevice    *UIAgentUserDevice    `json:"user_device,omitempty,optional"`    // 用户设备
}

type UIAgentProjectDevice struct {
	UDID  string `json:"udid"`  // 设备编号
	Token string `json:"token"` // 令牌（占用设备后得到的令牌）
}

type UIAgentUserDevice struct {
	UDID          string `json:"udid"`           // 设备编号
	RemoteAddress string `json:"remote_address"` // 远程连接地址
}

type SubEnvInfo struct {
	SubEnvName string `json:"sub_env_name" validate:"required" zh:"子环境名称"`
}

type TriggerUser struct {
	Email   string `json:"email" validate:"required" zh:"TT邮箱"`
	Account string `json:"account,omitempty,optional" zh:"TT工号"`
}

type UiPlanInfo struct {
	AppDownloadUrl string   `json:"app_download_url,omitempty,optional" validate:"omitempty,url,lte=255" zh:"app下载地址"`
	AppVersion     string   `json:"app_version,omitempty,optional" zh:"app版本信息"`
	Devices        []string `json:"devices,omitempty,optional" zh:"设备列表"`
	Together       bool     `json:"together,omitempty,optional" zh:"选择的设备是否一起执行"`
}

type PerfPlanInfo struct {
	ExecuteType             int8  `json:"execute_type" validate:"oneof=1 2" zh:"执行类型(执行、调试)"`
	EstimatedTime           int64 `json:"estimated_time,omitempty,optional" zh:"预计执行时间(秒级时间戳,0为立即执行)"`
	SendPreviewNotification bool  `json:"send_preview_notification,default=true" zh:"是否发送预告通知"`
}

type ApproverUser struct {
	Email   string `json:"email" validate:"required" zh:"TT邮箱"`
	Account string `json:"account,omitempty,optional" zh:"TT工号"`
}

type StopMetadata struct {
	StopType int32       `json:"stop_type,default=1" validate:"oneof=1 2" zh:"停止类型"`
	Reason   string      `json:"reason" zh:"停止原因"`
	Detail   *StopDetail `json:"detail,optional,omitempty" zh:"停止详细信息"`
}
type StopDetail struct {
	Rule *StopDetailOfPerfStopRule `json:"rule,optional,omitempty" zh:"压测停止规则"`
}
type StopDetailOfPerfStopRule struct {
	MetricType string        `json:"metric_type" validate:"required" zh:"指标类型"`
	Service    string        `json:"service" validate:"required" zh:"服务名称"`
	Namespace  string        `json:"namespace" validate:"required" zh:"命名空间"`
	Method     string        `json:"method" validate:"required" zh:"接口名称"`
	Threshold  float64       `json:"threshold" validate:"required" zh:"规则中的阀值"`
	ReachedAt  int64         `json:"reached_at" validate:"required" zh:"达到规则中的阀值的时间"`
	LatestAt   int64         `json:"latestAt" validate:"gtfield=ReachedAt" zh:"达到规则中的持续时长的时间"`
	Points     []MetricPoint `json:"points" validate:"lte=1" zh:"满足规则后的指标点"`
}
type MetricPoint struct {
	Timestamp int64
	Value     float64
}

type PlanReq struct {
	PlanId          string         `json:"plan_id" validate:"required" zh:"计划ID"`
	PlanType        int64          `json:"plan_type,omitempty,optional,default=4" validate:"oneof=4 9 93 94" zh:"执行对象的类型"`
	ProjectId       string         `json:"project_id" validate:"required" zh:"项目ID"`
	TriggerMode     string         `json:"trigger_mode" validate:"required" zh:"执行模式"`
	Debug           bool           `json:"debug,omitempty,optional" zh:"是否开启debug"`
	SubEnvInfo      *SubEnvInfo    `json:"sub_env_info,omitempty,optional" zh:"子环境信息"`
	TriggerUser     *TriggerUser   `json:"trigger_user,omitempty,optional" zh:"触发测试计划执行的人的信息"`
	CallbackUrl     string         `json:"callback_url,omitempty,optional" zh:"回调地址"`
	CallbackTimeout int64          `json:"callback_timeout,omitempty,optional" zh:"回调超时时间"`
	Services        []string       `json:"services,omitempty,optional" zh:"CI/CD升级的服务列表"`
	UiPlanInfo      *UiPlanInfo    `json:"ui_plan_info,omitempty,optional" zh:"UI测试计划额外信息"`
	PriorityType    int8           `json:"priority_type,omitempty,optional" validate:"oneof=0 1 2 3 4" zh:"优先级（Default、Middle、High、Ultra、Low)"`
	PerfPlanInfo    *PerfPlanInfo  `json:"perf_plan_info,omitempty,optional" zh:"压力测试计划额外信息"`
	Approvers       []ApproverUser `json:"approvers,omitempty,optional" zh:"审批测试执行工单的人的信息"`
}

type PlanResp struct {
	TaskId    string `json:"task_id"`
	ExecuteId string `json:"execute_id"`
}

type PublishReq struct {
	ComponentKey
	ProjectId        string                   `json:"project_id"`                            // 项目ID
	ExecuteMode      int32                    `json:"execute_mode"`                          // 用例执行模式
	AccountConfig    []types.ApiAccountConfig `json:"account_config,omitempty,optional"`     // 池账号配置
	GeneralConfig    types.ApiGeneralConfig   `json:"general_config,omitempty,optional"`     // 通用配置
	Debug            bool                     `json:"debug,omitempty,optional"`              // 是否验证服务器的TLS证书
	InterfaceCaseIds []ComponentKey           `json:"interface_case_ids,omitempty,optional"` // 接口用例id列表，当type!=5时，该值为null
	UIAgentComponent *UIAgentComponent        `json:"ui_agent_component,omitempty,optional"` // UI Agent组件
}

type PublishResp struct {
	TaskId    string `json:"task_id"`
	ExecuteId string `json:"execute_id"`
	Version   string `json:"version"`
}

type StopReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
	TaskId    string `json:"task_id" validate:"required" zh:"任务ID"`
	Id        string `json:"id" validate:"required" zh:"执行对象的ID"`
	Type      int64  `json:"type" validate:"required" zh:"执行对象的类型"`
	ExecuteId string `json:"execute_id" validate:"required" zh:"执行ID"`

	Metadata *StopMetadata `json:"metadata,optional,omitempty" zh:"元数据"`
}

type StopResp struct{}

type SearchTaskInfoItem struct {
	ProjectId   string `json:"project_id"`   // 项目ID
	TaskId      string `json:"task_id"`      // 任务id
	PlanId      string `json:"plan_id"`      // 计划ID
	PlanName    string `json:"plan_name"`    // 计划名称
	TriggerMode string `json:"trigger_mode"` // 触发类型

	TaskExecuteStatus  int8 `json:"task_execute_status"`  // 执行状态(0排队中,1执行中,2已完成,3已停止)
	PriorityType       int8 `json:"priority_type"`        // 优先级策略(0 1 2 3 4 =》Default、Middle、High、Ultra、Low)
	TaskExecutedResult int8 `json:"task_executed_result"` // 执行结果(0缺省,1成功,2失败,3异常)

	TotalCase     int64 `json:"total_case"`     // 总测试用例数
	FinishedCase  int64 `json:"finished_case"`  // 已经执行的测试用例数
	SuccessCase   int64 `json:"success_case"`   // 执行成功的测试用例数
	TotalSuite    int64 `json:"total_suite"`    // 测试集合总数
	FinishedSuite int64 `json:"finished_suite"` // 执行完的测试集合数
	SuccessSuite  int64 `json:"success_suite"`  // 执行成功的测试集合数

	ExecuteBy  *userinfo.FullUserInfo `json:"execute_by"`  // 创建者
	CostTime   int64                  `json:"cost_time"`   // 执行耗时
	CreateTime int64                  `json:"create_time"` // 创建时间
	WaitTime   int64                  `json:"wait_time"`   // 排队耗时
	StartedAt  int64                  `json:"started_at"`  // 开始时间
	EndedAt    int64                  `json:"ended_at"`    // 结束时间

	ExecuteId         string `json:"execute_id"  redis:"execute_id"`                   // 计划ID
	UpdateAt          int64  `json:"update_at"  redis:"update_at"`                     // 更新时间
	ReportViewUrl     string `json:"report_view_url"  redis:"report_view_url"`         // 查看地址
	ReportDownloadUrl string `json:"report_download_url"  redis:"report_download_url"` // 下载地址
}

type SearchTaskInfoReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchTaskInfoResp struct {
	CurrentPage uint64                `json:"current_page"`
	PageSize    uint64                `json:"page_size"`
	TotalCount  uint64                `json:"total_count"`
	TotalPage   uint64                `json:"total_page"`
	Items       []*SearchTaskInfoItem `json:"items"`
}
