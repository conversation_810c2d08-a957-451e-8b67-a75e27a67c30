// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common/ui_agent.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UIAgentComponentExpectation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UIAgentComponentExpectation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentComponentExpectation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentComponentExpectationMultiError, or nil if none found.
func (m *UIAgentComponentExpectation) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentComponentExpectation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	if m.GetImage() != "" {

		if !_UIAgentComponentExpectation_Image_Pattern.MatchString(m.GetImage()) {
			err := UIAgentComponentExpectationValidationError{
				field:  "Image",
				reason: "value does not match regex pattern \"(?:^file_id:.+?)\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return UIAgentComponentExpectationMultiError(errors)
	}

	return nil
}

// UIAgentComponentExpectationMultiError is an error wrapping multiple
// validation errors returned by UIAgentComponentExpectation.ValidateAll() if
// the designated constraints aren't met.
type UIAgentComponentExpectationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentComponentExpectationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentComponentExpectationMultiError) AllErrors() []error { return m }

// UIAgentComponentExpectationValidationError is the validation error returned
// by UIAgentComponentExpectation.Validate if the designated constraints
// aren't met.
type UIAgentComponentExpectationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentComponentExpectationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentComponentExpectationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentComponentExpectationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentComponentExpectationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentComponentExpectationValidationError) ErrorName() string {
	return "UIAgentComponentExpectationValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentComponentExpectationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentComponentExpectation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentComponentExpectationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentComponentExpectationValidationError{}

var _UIAgentComponentExpectation_Image_Pattern = regexp.MustCompile("(?:^file_id:.+?)")

// Validate checks the field values on UIAgentComponentStep with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UIAgentComponentStep) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentComponentStep with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentComponentStepMultiError, or nil if none found.
func (m *UIAgentComponentStep) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentComponentStep) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetContent()) < 1 {
		err := UIAgentComponentStepValidationError{
			field:  "Content",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetExpectation() == nil {
		err := UIAgentComponentStepValidationError{
			field:  "Expectation",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExpectation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentStepValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentStepValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentStepValidationError{
				field:  "Expectation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UIAgentComponentStepMultiError(errors)
	}

	return nil
}

// UIAgentComponentStepMultiError is an error wrapping multiple validation
// errors returned by UIAgentComponentStep.ValidateAll() if the designated
// constraints aren't met.
type UIAgentComponentStepMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentComponentStepMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentComponentStepMultiError) AllErrors() []error { return m }

// UIAgentComponentStepValidationError is the validation error returned by
// UIAgentComponentStep.Validate if the designated constraints aren't met.
type UIAgentComponentStepValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentComponentStepValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentComponentStepValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentComponentStepValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentComponentStepValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentComponentStepValidationError) ErrorName() string {
	return "UIAgentComponentStepValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentComponentStepValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentComponentStep.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentComponentStepValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentComponentStepValidationError{}
