Name: rpc.reporter
Mode: test
ListenOn: 127.0.0.1:20511
Timeout: 0

Log:
  ServiceName: rpc.reporter
  Encoding: plain
  Level: info
  Path: /app/logs/reporter

Prometheus:
  Host: 0.0.0.0
  Port: 20522
  Path: /metrics

Telemetry:
  Name: rpc.reporter
  Endpoint: http://127.0.0.1:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

DevServer:
  Enabled: true
  Port: 20532

DB:
#  DataSource: root:Quwan@2020@tcp(127.0.0.1:3306)/reporter?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
  DataSource: probe:Quwan@2020_TTinternation@tcp(************:3306)/reporter?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

Redis:
  Key: rpc.reporter
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 5

Cache:
  - Host: 127.0.0.1:6379
    Pass:
    DB: 5

ProbeDomain: https://testing-dev-quality.ttyuyin.com/ # 测试
#ProbeDomain: https://dev-quality.ttyuyin.com/ # 生产

Monitors:
  - Env: PRODUCTION
    GrafanaBaseURL: https://yw-monitor.ttyuyin.com/d/yaKGumVIk/andgrpc?orgId=7&refresh=10s
    AppInsightBaseURL: https://tt-telemetry.ttyuyin.com/#/application-dashboard
