syntax = "proto3";

package common;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb";

import "validate/validate.proto";


// UIAgentComponentExpectation UIAgent组件的期望结果
message UIAgentComponentExpectation {
  string text = 1; // 文本
  string image = 2 [(validate.rules).string = {ignore_empty: true pattern: "(?:^file_id:.+?)"}]; // 图片
}

// UIAgentComponentStep UIAgent组件的步骤
message UIAgentComponentStep {
  string content = 1 [(validate.rules).string = {min_len: 1}]; // 步骤内容
  UIAgentComponentExpectation expectation = 2 [(validate.rules).message = {required: true}]; // 期望结果
}
