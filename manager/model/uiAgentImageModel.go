package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ UiAgentImageModel = (*customUiAgentImageModel)(nil)

	uiAgentImageInsertFields = stringx.Remove(
		uiAgentImageFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// UiAgentImageModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiAgentImageModel.
	UiAgentImageModel interface {
		uiAgentImageModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *UiAgentImage) squirrel.InsertBuilder
		UpdateBuilder(data *UiAgentImage) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*UiAgentImage, error)

		RemoveByImageID(ctx context.Context, session sqlx.Session, projectID, imageID string) (sql.Result, error)
	}

	customUiAgentImageModel struct {
		*defaultUiAgentImageModel

		conn sqlx.SqlConn
	}
)

// NewUiAgentImageModel returns a model for the database table.
func NewUiAgentImageModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) UiAgentImageModel {
	return &customUiAgentImageModel{
		defaultUiAgentImageModel: newUiAgentImageModel(conn, c, opts...),
		conn:                     conn,
	}
}

func (m *customUiAgentImageModel) Table() string {
	return m.table
}

func (m *customUiAgentImageModel) Fields() []string {
	return uiAgentImageFieldNames
}

func (m *customUiAgentImageModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customUiAgentImageModel) InsertBuilder(data *UiAgentImage) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(uiAgentImageInsertFields...).Values(
		data.ProjectId, data.ImageId, data.Name, data.Description, data.Extension, data.Hash, data.Size, data.Path,
		data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customUiAgentImageModel) UpdateBuilder(data *UiAgentImage) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":        data.Name,
		"`description`": data.Description,
		"`hash`":        data.Hash,
		"`size`":        data.Size,
		"`path`":        data.Path,
		"`deleted`":     data.Deleted,
		"`updated_by`":  data.UpdatedBy,
		"`deleted_by`":  data.DeletedBy,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customUiAgentImageModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiAgentImageFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiAgentImageModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiAgentImageModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customUiAgentImageModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*UiAgentImage, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiAgentImage
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customUiAgentImageModel) RemoveByImageID(
	ctx context.Context, session sqlx.Session, projectID, imageID string,
) (sql.Result, error) {
	keys := m.getKeysByImageID(ctx, projectID, imageID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE FROM `ui_agent_image`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ? AND `image_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `image_id` = ?", projectID, imageID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customUiAgentImageModel) getKeysByImageID(ctx context.Context, projectID, imageID string) []string {
	c, err := m.FindOneByProjectIdImageId(ctx, projectID, imageID)
	if err != nil {
		return make([]string, 0)
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerUiAgentImageIdPrefix, c.Id),
		fmt.Sprintf("%s%v:%v", cacheManagerUiAgentImageProjectIdImageIdPrefix, c.ProjectId, c.ImageId),
	}
}
