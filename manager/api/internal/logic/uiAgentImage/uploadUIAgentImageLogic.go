package uiAgentImage

import (
	"context"
	"database/sql"
	"encoding/hex"
	"image"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type UploadUIAgentImageLogic struct {
	*BaseLogic
}

func NewUploadUIAgentImageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UploadUIAgentImageLogic {
	return &UploadUIAgentImageLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *UploadUIAgentImageLogic) UploadUIAgentImage(req *types.UploadUIAgentImageReq) (resp *types.UploadUIAgentImageResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, req.ProjectId); err != nil {
		return nil, err
	}

	// validate the image file
	if !isImage(req.ImageFile) {
		return nil, errorx.Errorf(
			errorx.ValidateParamError,
			"invalid image file, filename: %s, expected image type: gif, jpeg, png",
			req.ImageFileHeader.Filename,
		)
	}

	// generate the image id
	imageID, err := l.generateImageID(req.ProjectId)
	if err != nil {
		return nil, err
	}

	// save the file to the local file system
	localFilePath := filepath.Join(
		l.svcCtx.Config.PVCPath, constants.ConstStoragePathOfFiles, common.ConstUIAgentDirectory, req.ProjectId, imageID,
	)
	defer func() {
		if err != nil {
			_ = os.Remove(localFilePath)
		}
	}()

	md5Hash, size, err := logic.SaveFile(req.ImageFile, localFilePath)
	if err != nil {
		return nil, err
	} else if size != req.ImageFileHeader.Size {
		return nil, errorx.Errorf(
			errorx.FileOperationFailure,
			"the size of file is mismatch, filename: %s, expected: %d, actual: %d",
			req.ImageFileHeader.Filename, req.ImageFileHeader.Size, size,
		)
	}

	fileExt := filepath.Ext(req.ImageFileHeader.Filename)
	now := time.Now()
	uiAgentImage := &model.UiAgentImage{
		ProjectId: req.ProjectId,
		ImageId:   imageID,
		Name:      strings.TrimSuffix(req.ImageFileHeader.Filename, fileExt),
		Description: sql.NullString{
			String: req.ImageFileHeader.Filename,
			Valid:  true,
		},
		Extension: fileExt,
		Hash:      hex.EncodeToString(md5Hash.Sum(nil)),
		Size:      size,
		Path:      localFilePath,
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: now,
		UpdatedAt: now,
	}
	if err = l.svcCtx.UIAgentImageModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.UIAgentImageModel.Insert(context, session, uiAgentImage); err != nil {
				return errorx.Errorf(
					errorx.DBError,
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.UIAgentImageModel.Table(), jsonx.MarshalIgnoreError(uiAgentImage), err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return &types.UploadUIAgentImageResp{ImageId: imageID}, nil
}

// isImage checks if the file is an image.
func isImage(file multipart.File) bool {
	defer func() {
		if file != nil {
			_, _ = file.Seek(0, io.SeekStart)
		}
	}()

	_, _, err := image.Decode(file)
	return err == nil
}
