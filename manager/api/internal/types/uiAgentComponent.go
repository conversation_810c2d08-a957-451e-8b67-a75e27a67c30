package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type UIAgentComponent struct {
	ProjectId        string                             `json:"project_id"`
	CategoryId       string                             `json:"category_id"`
	ComponentId      string                             `json:"component_id"`
	Name             string                             `json:"name"`
	Description      string                             `json:"description"`
	Tags             []string                           `json:"tags"`
	PlatformType     int64                              `json:"platform_type"`
	ApplicationId    string                             `json:"application_id"`
	StepByStep       bool                               `json:"step_by_step"`
	Steps            []*types.UIAgentComponentStep      `json:"steps"`
	Expectation      *types.UIAgentComponentExpectation `json:"expectation"`
	Variables        []*types.KeyValuePair              `json:"variables"`
	LatestExecutedAt int64                              `json:"latest_executed_at"`
	LatestResult     int64                              `json:"latest_result"`
	MaintainedBy     *userinfo.FullUserInfo             `json:"maintained_by"`
	CreatedBy        *userinfo.FullUserInfo             `json:"created_by"`
	UpdatedBy        *userinfo.FullUserInfo             `json:"updated_by"`
	CreatedAt        int64                              `json:"created_at"`
	UpdatedAt        int64                              `json:"updated_at"`
}

type CreateUIAgentComponentReq struct {
	ProjectId     string                             `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId    string                             `json:"category_id" validate:"required" zh:"分类ID"`
	Name          string                             `json:"name" validate:"gte=1,lte=64" zh:"组件名称"`
	Description   string                             `json:"description" validate:"lte=255" zh:"组件描述"`
	Tags          []string                           `json:"tags" validate:"gte=0" zh:"组件标签"`
	ApplicationId string                             `json:"application_id" validate:"required" zh:"应用配置ID"`
	StepByStep    bool                               `json:"step_by_step" validate:"required" zh:"是否分步执行"`
	Steps         []*types.UIAgentComponentStep      `json:"steps" validate:"gte=1,dive,required" zh:"步骤列表"`
	Expectation   *types.UIAgentComponentExpectation `json:"expectation" zh:"期望结果"`
	Variables     []*types.KeyValuePair              `json:"variables" validate:"gte=0" zh:"变量列表"`
	MaintainedBy  string                             `json:"maintained_by" validate:"lte=64" zh:"组件维护者"`
}

type CreateUIAgentComponentResp struct {
	ComponentId string `json:"component_id"`
}

type RemoveUIAgentComponentReq struct {
	ProjectId    string   `json:"project_id" validate:"required" zh:"项目ID"`
	ComponentIds []string `json:"component_ids" validate:"gt=0" zh:"组件ID列表"`
}

type RemoveUIAgentComponentResp struct{}

type ModifyUIAgentComponentReq struct {
	ProjectId     string                             `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId    string                             `json:"category_id" validate:"required" zh:"分类ID"`
	ComponentId   string                             `json:"component_id" validate:"required" zh:"组件ID"`
	Name          string                             `json:"name" validate:"gte=1,lte=64" zh:"组件名称"`
	Description   string                             `json:"description" validate:"lte=255" zh:"组件描述"`
	Tags          []string                           `json:"tags" validate:"gte=0" zh:"组件标签"`
	ApplicationId string                             `json:"application_id" validate:"required" zh:"应用配置ID"`
	StepByStep    bool                               `json:"step_by_step" validate:"required" zh:"是否分步执行"`
	Steps         []*types.UIAgentComponentStep      `json:"steps" validate:"gte=1,dive,required" zh:"步骤列表"`
	Expectation   *types.UIAgentComponentExpectation `json:"expectation" zh:"期望结果"`
	Variables     []*types.KeyValuePair              `json:"variables" validate:"gte=0" zh:"变量列表"`
	MaintainedBy  string                             `json:"maintained_by" validate:"lte=64" zh:"组件维护者"`
}

type ModifyUIAgentComponentResp struct{}

type SearchUIAgentComponentReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId string           `json:"category_id" validate:"required" zh:"分类ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchUIAgentComponentResp struct {
	CurrentPage uint64              `json:"current_page"`
	PageSize    uint64              `json:"page_size"`
	TotalCount  uint64              `json:"total_count"`
	TotalPage   uint64              `json:"total_page"`
	Items       []*UIAgentComponent `json:"items"`
}

type ViewUIAgentComponentReq struct {
	ProjectId   string `form:"project_id" validate:"required" zh:"项目ID"`
	ComponentId string `form:"component_id" validate:"required" zh:"组件ID"`
}

type ViewUIAgentComponentResp struct {
	*UIAgentComponent
}
