package uiagentcomponentservicelogic

import (
	"testing"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

func Test_getVariablesFromSteps(t *testing.T) {
	tests := []struct {
		name    string
		steps   []*commonpb.UIAgentComponentStep
		want    []string
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid variables in content and expectation text",
			steps: []*commonpb.UIAgentComponentStep{
				{
					Content: "Click button with text {{.buttonText}}",
					Expectation: &commonpb.UIAgentComponentExpectation{
						Text: "Expected result: {{.expectedResult}}",
					},
				},
				{
					Content: "Input {{.username}} in username field",
					Expectation: &commonpb.UIAgentComponentExpectation{
						Text: "Login with {{.password}}",
					},
				},
			},
			want:    []string{"buttonText", "expectedResult", "username", "password"},
			wantErr: false,
		},
		{
			name: "duplicate variables should be deduplicated",
			steps: []*commonpb.UIAgentComponentStep{
				{
					Content: "Use {{.username}} to login",
					Expectation: &commonpb.UIAgentComponentExpectation{
						Text: "Welcome {{.username}}",
					},
				},
			},
			want:    []string{"username"},
			wantErr: false,
		},
		{
			name: "no variables",
			steps: []*commonpb.UIAgentComponentStep{
				{
					Content: "Click login button",
					Expectation: &commonpb.UIAgentComponentExpectation{
						Text: "Login successful",
					},
				},
			},
			want:    []string{},
			wantErr: false,
		},
		{
			name: "invalid variable name with special characters",
			steps: []*commonpb.UIAgentComponentStep{
				{
					Content: "Click {{.button-text}}",
					Expectation: &commonpb.UIAgentComponentExpectation{
						Text: "Success",
					},
				},
			},
			want:    nil,
			wantErr: true,
			errMsg:  "invalid variable name 'button-text': does not match Go identifier rules",
		},
		{
			name: "invalid variable name starting with number",
			steps: []*commonpb.UIAgentComponentStep{
				{
					Content: "Click {{.1button}}",
					Expectation: &commonpb.UIAgentComponentExpectation{
						Text: "Success",
					},
				},
			},
			want:    nil,
			wantErr: true,
			errMsg:  "invalid variable name '1button': does not match Go identifier rules",
		},
		{
			name: "Go keyword as variable name",
			steps: []*commonpb.UIAgentComponentStep{
				{
					Content: "Use {{.func}} as parameter",
					Expectation: &commonpb.UIAgentComponentExpectation{
						Text: "Success",
					},
				},
			},
			want:    nil,
			wantErr: true,
			errMsg:  "invalid variable name 'func': cannot use Go language keyword as variable name",
		},
		{
			name: "valid variable names with underscores and numbers",
			steps: []*commonpb.UIAgentComponentStep{
				{
					Content: "Use {{._private}} and {{.var_1}} and {{.VAR_2}}",
					Expectation: &commonpb.UIAgentComponentExpectation{
						Text: "Result: {{.result_123}}",
					},
				},
			},
			want:    []string{"_private", "var_1", "VAR_2", "result_123"},
			wantErr: false,
		},
		{
			name: "empty steps",
			steps: []*commonpb.UIAgentComponentStep{},
			want:    []string{},
			wantErr: false,
		},
		{
			name: "nil step in slice",
			steps: []*commonpb.UIAgentComponentStep{
				nil,
				{
					Content: "Use {{.validVar}}",
					Expectation: &commonpb.UIAgentComponentExpectation{
						Text: "Success",
					},
				},
			},
			want:    []string{"validVar"},
			wantErr: false,
		},
		{
			name: "nil expectation",
			steps: []*commonpb.UIAgentComponentStep{
				{
					Content:     "Use {{.validVar}}",
					Expectation: nil,
				},
			},
			want:    []string{"validVar"},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getVariablesFromSteps(tt.steps)
			
			if tt.wantErr {
				if err == nil {
					t.Errorf("getVariablesFromSteps() error = nil, wantErr %v", tt.wantErr)
					return
				}
				if tt.errMsg != "" && err.Error() != tt.errMsg {
					t.Errorf("getVariablesFromSteps() error = %v, want error message %v", err.Error(), tt.errMsg)
				}
				return
			}
			
			if err != nil {
				t.Errorf("getVariablesFromSteps() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			
			// Convert slice to map for comparison (order doesn't matter)
			gotMap := make(map[string]bool)
			for _, v := range got {
				gotMap[v] = true
			}
			
			wantMap := make(map[string]bool)
			for _, v := range tt.want {
				wantMap[v] = true
			}
			
			if len(gotMap) != len(wantMap) {
				t.Errorf("getVariablesFromSteps() got %v variables, want %v variables", len(gotMap), len(wantMap))
				return
			}
			
			for v := range wantMap {
				if !gotMap[v] {
					t.Errorf("getVariablesFromSteps() missing variable %v", v)
				}
			}
			
			for v := range gotMap {
				if !wantMap[v] {
					t.Errorf("getVariablesFromSteps() unexpected variable %v", v)
				}
			}
		})
	}
}
