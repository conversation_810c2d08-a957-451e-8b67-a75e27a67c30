package uiagentcomponentservicelogic

import (
	"context"
	"fmt"
	"path/filepath"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	tagservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	createTagLogic *tagservicelogic.CreateTagLogic

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		createTagLogic: tagservicelogic.NewCreateTagLogic(ctx, svcCtx),

		converters: []qetutils.TypeConverter{
			logic.SqlNullStringToTags(),                        // tags
			logic.SqlNullStringToUIAgentComponentExpectation(), // ui_agent_component_expectation
			logic.StringToUIAgentComponentSteps(),              // ui_agent_component_steps
		},
	}
}

func (l *BaseLogic) Context() context.Context {
	return l.ctx
}

func (l *BaseLogic) ServiceContext() *svc.ServiceContext {
	return l.svcCtx
}

func (l *BaseLogic) generateComponentID(projectID string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenUIAgentComponentID), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.UIAgentComponentModel.FindOneByProjectIdComponentId(l.ctx, projectID, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	componentID := g.Next()
	if componentID == "" {
		return "", errorx.Err(
			errorx.GenerateUniqueIdFailure, "failed to generate ui agent component id, please try it later",
		)
	}

	return componentID, nil
}

// generateLockKey 生成分布式锁的key
func (l *BaseLogic) generateLockKey(projectID, componentID string) string {
	return fmt.Sprintf("%s:%s:%s", common.ConstLockUIAgentComponentProjectIDComponentIDPrefix, projectID, componentID)
}

func (l *BaseLogic) getImagesFromSteps(projectID string, steps []*commonpb.UIAgentComponentStep) (
	[]*model.UiAgentImage, error,
) {
	images := make([]*model.UiAgentImage, 0, len(steps))
	if err := mr.MapReduceVoid[string, *model.UiAgentImage](
		func(source chan<- string) {
			cache := make(map[string]lang.PlaceholderType, len(steps))
			for _, step := range steps {
				imageID := step.GetExpectation().GetImage()
				if len(imageID) == 0 {
					continue
				}

				if _, ok := cache[imageID]; ok {
					continue
				}
				cache[imageID] = lang.Placeholder

				source <- imageID
			}
		},
		func(item string, writer mr.Writer[*model.UiAgentImage], cancel func(error)) {
			var (
				image *model.UiAgentImage
				err   error
			)
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			if image, err = l.getImageByImageID(projectID, item); err != nil {
				return
			}

			writer.Write(image)
		},
		func(pipe <-chan *model.UiAgentImage, cancel func(error)) {
			for item := range pipe {
				if item == nil {
					continue
				}

				images = append(images, item)
			}
		},
		mr.WithContext(l.ctx),
	); err != nil {
		return nil, err
	}

	return images, nil
}

func (l *BaseLogic) getImageByImageID(projectID, imageID string) (*model.UiAgentImage, error) {
	image, err := model.CheckUIAgentImageByImageID(l.ctx, l.svcCtx.UIAgentImageModel, projectID, imageID)
	if err != nil {
		return nil, err
	}

	if !l.checkImageIsExist(projectID, image.ImageId) {
		return nil, errorx.Errorf(
			errorx.NotExists,
			"image not found in file system, project_id: %s, image_id: %s, name: %s",
			projectID, image.ImageId, image.Name,
		)
	}

	return image, nil
}

func (l *BaseLogic) checkImageIsExist(projectID, imageID string) bool {
	if len(l.svcCtx.Config.PVCPath) == 0 {
		return false
	}

	filePath := filepath.Join(
		l.svcCtx.Config.PVCPath, constants.ConstStoragePathOfFiles, common.ConstUIAgentDirectory, projectID, imageID,
	)
	return qetutils.Exists(filePath)
}

func (l *BaseLogic) getUserInfoByAccount(account string) (*userpb.UserInfo, error) {
	resp, err := l.svcCtx.UserRpc.ViewUser(
		l.ctx, &userpb.GetUserReq{
			Account: account,
		},
	)
	if err != nil {
		return nil, err
	}

	return resp.GetUserInfo(), nil
}

func (l *BaseLogic) updateApplicationConfigRelationship(
	ctx context.Context, session sqlx.Session, component *model.UiAgentComponent, applicationID string,
) error {
	var (
		projectID   = component.ProjectId
		componentID = component.ComponentId

		now = time.Now()
	)

	if ctx == nil {
		ctx = l.ctx
	}

	rs, err := l.svcCtx.ApplicationConfigReferenceModel.FindByReference(
		ctx, projectID, common.ConstReferenceTypeUIAgentComponent, componentID,
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find application configuration relationship, project_id: %s, component_id: %s, error: %+v",
			projectID, componentID, err,
		)
	}

	if applicationID == "" && len(rs) == 0 {
		return nil
	}

	from := make(map[string]*model.ApplicationConfigurationReferenceRelationship, len(rs))
	for _, r := range rs {
		if r == nil {
			continue
		}

		from[r.ConfigId] = r
	}

	if _, ok := from[applicationID]; !ok {
		item := &model.ApplicationConfigurationReferenceRelationship{
			ProjectId:     projectID,
			ReferenceType: common.ConstReferenceTypeUIAgentComponent,
			ReferenceId:   componentID,
			ConfigId:      applicationID,
			CreatedBy:     l.currentUser.Account,
			UpdatedBy:     l.currentUser.Account,
			CreatedAt:     now,
			UpdatedAt:     now,
		}
		if _, err = l.svcCtx.ApplicationConfigReferenceModel.Insert(ctx, session, item); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to insert values to table, table: %s, values: %s, error: %+v",
				l.svcCtx.ApplicationConfigReferenceModel.Table(), jsonx.MarshalIgnoreError(item), err,
			)
		}
	}

	for key, val := range from {
		if key == applicationID {
			continue
		}

		if err = l.svcCtx.ApplicationConfigReferenceModel.Delete(ctx, session, val.Id); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to delete item from table, table: %s, item: %s, error: %+v",
				l.svcCtx.ApplicationConfigReferenceModel.Table(), jsonx.MarshalIgnoreError(val), err,
			)
		}
	}

	return nil
}

func (l *BaseLogic) updateImageRelationship(
	ctx context.Context, session sqlx.Session, component *model.UiAgentComponent, images []*model.UiAgentImage,
) error {
	var (
		projectID   = component.ProjectId
		componentID = component.ComponentId

		now = time.Now()
	)

	if ctx == nil {
		ctx = l.ctx
	}

	rs, err := l.svcCtx.UIAgentImageReferenceModel.FindByReference(
		ctx, projectID, common.ConstReferenceTypeUIAgentComponent, componentID,
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find ui agent image reference relationship, project_id: %s, component_id: %s, error: %+v",
			projectID, componentID, err,
		)
	}

	if len(images) == 0 && len(rs) == 0 {
		return nil
	}

	from := make(map[string]*model.UiAgentImageReferenceRelationship, len(rs))
	for _, r := range rs {
		if r == nil {
			continue
		}

		from[r.ImageId] = r
	}

	to := make(map[string]*model.UiAgentImage, len(images))
	for _, image := range images {
		if image == nil {
			continue
		}

		to[image.ImageId] = image

		if _, ok := from[image.ImageId]; !ok {
			item := &model.UiAgentImageReferenceRelationship{
				ProjectId:     projectID,
				ReferenceType: common.ConstReferenceTypeUIAgentComponent,
				ReferenceId:   componentID,
				ImageId:       image.ImageId,
				CreatedBy:     l.currentUser.Account,
				UpdatedBy:     l.currentUser.Account,
				CreatedAt:     now,
				UpdatedAt:     now,
			}
			if _, err = l.svcCtx.UIAgentImageReferenceModel.Insert(ctx, session, item); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.UIAgentImageReferenceModel.Table(), jsonx.MarshalIgnoreError(item), err,
				)
			}
		}
	}

	for _, item := range from {
		if item == nil {
			continue
		}

		if _, ok := to[item.ImageId]; !ok {
			if err = l.svcCtx.UIAgentImageReferenceModel.Delete(ctx, session, item.Id); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to delete item from table, table: %s, item: %s, error: %+v",
					l.svcCtx.UIAgentImageReferenceModel.Table(), jsonx.MarshalIgnoreError(item), err,
				)
			}
		}
	}

	return nil
}
