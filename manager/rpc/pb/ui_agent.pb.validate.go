// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/ui_agent.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.PlatformType(0)
)

// Validate checks the field values on UIAgentComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UIAgentComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentComponentMultiError, or nil if none found.
func (m *UIAgentComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for ComponentId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for PlatformType

	// no validation rules for ApplicationId

	// no validation rules for StepByStep

	for idx, item := range m.GetSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentComponentValidationError{
						field:  fmt.Sprintf("Steps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentComponentValidationError{
						field:  fmt.Sprintf("Steps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentComponentValidationError{
					field:  fmt.Sprintf("Steps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetExpectation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentValidationError{
				field:  "Expectation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVariables() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentComponentValidationError{
						field:  fmt.Sprintf("Variables[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentComponentValidationError{
						field:  fmt.Sprintf("Variables[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentComponentValidationError{
					field:  fmt.Sprintf("Variables[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for LatestExecutedAt

	// no validation rules for LatestResult

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return UIAgentComponentMultiError(errors)
	}

	return nil
}

// UIAgentComponentMultiError is an error wrapping multiple validation errors
// returned by UIAgentComponent.ValidateAll() if the designated constraints
// aren't met.
type UIAgentComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentComponentMultiError) AllErrors() []error { return m }

// UIAgentComponentValidationError is the validation error returned by
// UIAgentComponent.Validate if the designated constraints aren't met.
type UIAgentComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentComponentValidationError) ErrorName() string { return "UIAgentComponentValidationError" }

// Error satisfies the builtin error interface
func (e UIAgentComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentComponentValidationError{}
