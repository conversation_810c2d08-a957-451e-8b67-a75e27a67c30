Name: rpc.manager
Mode: test
ListenOn: 127.0.0.1:20211
Timeout: 0
MethodTimeouts:
  - FullMethod: /manager.ApiCaseService/ModifyApiCase
    Timeout: 8s
  - FullMethod: /manager.UiCaseService/ModifyUiCase
    Timeout: 8s
  - FullMethod: /manager.UiCaseService/RemoveUiCase
    Timeout: 8s
  - FullMethod: /manager.UiCaseService/ViewUiCase
    Timeout: 8s
  - FullMethod: /manager.CategoryService/GetCategoryTree
    Timeout: 8s
  - FullMethod: /manager.InterfaceDefinitionService/LocalImportInterfaceDefinition
    Timeout: 9m
  - FullMethod: /manager.ProjectDeviceService/AcquireProjectDevice
    Timeout: 8s
  - FullMethod: /manager.ProjectDeviceService/ReleaseProjectDevice
    Timeout: 8s

Log:
  ServiceName: rpc.manager
  Encoding: plain
  Level: info
  Path: /app/logs/manager

#Prometheus:
#  Host: 0.0.0.0
#  Port: 20222
#  Path: /metrics

#Telemetry:
#  Name: rpc.manager
#  Endpoint: http://127.0.0.1:14268/api/traces
#  Sampler: 1.0
#  Batcher: jaeger

#DevServer:
#  Enabled: true
#  Port: 20232

#Etcd:
#  Hosts:
#  - 127.0.0.1:2379
#  Key: rpc.manager

Redis:
  Key: rpc.manager
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 2

Cache:
  - Host: 127.0.0.1:6379
    Pass:
    DB: 2

DB:
#  DataSource: root:Quwan@2020@tcp(127.0.0.1:3306)/manager?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
  DataSource: probe:Quwan@2020_TTinternation@tcp(************:3306)/manager?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

Beat:
  Endpoints:
    - 127.0.0.1:10411
  NonBlock: true

DeviceHub:
  Endpoints:
    - 127.0.0.1:20811
  NonBlock: true

LarkProxy:
  Endpoints:
    - 127.0.0.1:21311
  NonBlock: true

Notifier:
  Endpoints:
    - 127.0.0.1:10311
  NonBlock: true

Permission:
  Endpoints:
    - 127.0.0.1:10211
  NonBlock: true
  Timeout: 0

Relation:
  Endpoints:
    - 127.0.0.1:20711
  NonBlock: true
  Timeout: 0

Reporter:
  Endpoints:
    - 127.0.0.1:20511
  NonBlock: true
  Timeout: 0

User:
  Endpoints:
    - 127.0.0.1:10111
  NonBlock: true
  Timeout: 0

WorkerConsumerV1:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mq:parse_python_project_task
  ConsumerTag: mqc_worker
  Db: 14
  MaxWorker: 0

WorkerConsumerV2:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mq:parse_python_project_task
  ConsumerTag: mqc_worker
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 14
  MaxWorker: 0

WorkerConsumerV1Producer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mq:parse_python_project_task
  Db: 14

WorkerProducerV1:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:ui_worker
  Db: 14

UIWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:ui_worker
  Db: 14

ManagerConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:manager
  ConsumerTag: mqc:manager
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 20
  MaxWorker: 512

ManagerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:manager
  Db: 20

LarkProxyConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:larkproxy
  ConsumerTag: mqc:manager
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 20
  MaxWorker: 8

DispatcherProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:dispatcher
  Db: 20

FailedCaseCleaner:
  CronExpression: "30 1 * * ?" # at 1:30 every day
  KeepDays: 10

UpdateInterfaceDefinition:
  CronExpression: "11 21 * * 4" # at 21:11 every Wednesday
  KeepDays: 7

UpdateInterfaceCoverage:
  CronExpression: "22 22 * * ?" # at 22:22 every day
  KeepDays: 90

UpdateInterfaceMetricsReference:
  CronExpression: "0 7 * * MON-FRI" # at 7:00 every weekday
  KeepDays: 0

UpdateAdvancedNotification:
  CronExpression: "0 9 * * MON-FRI" # at 9:00 every weekday
  KeepDays: 0

DeleteDisabledDevice:
  CronExpression: "30 9,13,18 * * ?" # at 9:30, 13:30, 18:30 every day
  KeepDays: 0

GitLab:
  Token: ********************

CMDB:
  BaseURL: http://yw-inner-cmdb.ttyuyin.com:5000
  Authorization: "B7Hr2AWz9UtQWjUpLpQIFI0i3fxYtCagY9njB3aK1sdiM9YBmFRraNqAmxwXIu5m"

AppInsight:
  BaseURL: http://tt-telemetry-web.ttyuyin.com

PVCPath: ${PVC_PATH}

StarProbeURL: https://dev-quality.ttyuyin.com
